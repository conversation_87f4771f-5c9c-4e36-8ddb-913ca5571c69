{% extends "base.html" %}

{% block title %}تفاصيل الدين - {{ debt.creditor_name }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="text-white text-center mb-4">
            <i class="fas fa-file-invoice-dollar me-3"></i>
            تفاصيل دين "{{ debt.creditor_name }}"
        </h1>
    </div>
</div>

<!-- معلومات الدين الأساسية -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الدين الأساسية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-muted">اسم الدائن/الجهة</h6>
                        <h4 class="text-primary">{{ debt.creditor_name }}</h4>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">نوع الدين</h6>
                        <h4><span class="badge bg-info fs-6">{{ debt.debt_type }}</span></h4>
                    </div>
                </div>
                
                <hr>
                
                <div class="row">
                    <div class="col-md-3">
                        <h6 class="text-muted">المبلغ الأصلي</h6>
                        <h4 class="text-warning">{{ debt.total_amount | currency }}</h4>
                    </div>
                    <div class="col-md-3">
                        <h6 class="text-muted">المبلغ المدفوع</h6>
                        <h4 class="text-success">{{ total_paid_amount | currency }}</h4>
                    </div>
                    <div class="col-md-3">
                        <h6 class="text-muted">المبلغ المتبقي</h6>
                        <h4 class="text-danger">{{ debt.remaining_amount | currency }}</h4>
                    </div>
                    <div class="col-md-3">
                        <h6 class="text-muted">نسبة السداد</h6>
                        <h4 class="text-primary">{{ "%.1f"|format(debt.progress_percentage) }}%</h4>
                    </div>
                </div>
                
                <!-- شريط التقدم -->
                <div class="mt-3">
                    <div class="progress" style="height: 25px;">
                        <div class="progress-bar 
                            {% if debt.progress_percentage >= 75 %}bg-success
                            {% elif debt.progress_percentage >= 50 %}bg-warning
                            {% elif debt.progress_percentage >= 25 %}bg-info
                            {% else %}bg-danger{% endif %}" 
                            role="progressbar" 
                            style="width: {{ debt.progress_percentage }}%">
                            {{ "%.1f"|format(debt.progress_percentage) }}% مكتمل
                        </div>
                    </div>
                </div>
                
                {% if debt.description %}
                <hr>
                <div class="row">
                    <div class="col-12">
                        <h6 class="text-muted">الوصف</h6>
                        <p class="mb-0">{{ debt.description }}</p>
                    </div>
                </div>
                {% endif %}
                
                <!-- معلومات إضافية -->
                <hr>
                <div class="row">
                    {% if debt.monthly_payment %}
                    <div class="col-md-3">
                        <h6 class="text-muted">القسط الشهري</h6>
                        <p class="mb-0 text-success fw-bold">{{ debt.monthly_payment | currency }}</p>
                    </div>
                    {% endif %}
                    
                    {% if debt.installments_count %}
                    <div class="col-md-3">
                        <h6 class="text-muted">عدد الأقساط</h6>
                        <p class="mb-0">{{ debt.paid_installments }}/{{ debt.installments_count }}</p>
                    </div>
                    {% endif %}
                    
                    {% if debt.monthly_due_date %}
                    <div class="col-md-3">
                        <h6 class="text-muted">يوم الاستحقاق</h6>
                        <p class="mb-0">{{ debt.monthly_due_date }} من كل شهر</p>
                    </div>
                    {% endif %}
                    
                    <div class="col-md-3">
                        <h6 class="text-muted">تاريخ الإضافة</h6>
                        <p class="mb-0">{{ debt.date_added.strftime('%Y-%m-%d') }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- أزرار العمليات -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cogs me-2 text-primary"></i>
                        العمليات المتاحة
                    </h5>
                    <div>
                        {% if debt.remaining_amount > 0 %}
                        <a href="{{ url_for('add_payment', debt_id=debt.id) }}" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>
                            إضافة دفعة جديدة
                        </a>
                        {% endif %}
                        <a href="{{ url_for('edit_debt', debt_id=debt.id) }}" class="btn btn-warning">
                            <i class="fas fa-edit me-2"></i>
                            تعديل الدين
                        </a>
                        <a href="{{ url_for('debts') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>
                            العودة للديون
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول الدفعات -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-receipt me-2"></i>
                    سجل الدفعات ({{ total_payments }} دفعة)
                </h5>
            </div>
            <div class="card-body">
                {% if payments %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>المبلغ</th>
                                    <th>تاريخ الدفعة</th>
                                    <th>رقم الوصل</th>
                                    <th>الملاحظات</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>العمليات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for payment in payments %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td class="text-success fw-bold">
                                        {{ payment.amount | currency }}
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">
                                            {{ payment.payment_date.strftime('%Y-%m-%d') }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if payment.receipt_number %}
                                            <span class="badge bg-info">{{ payment.receipt_number }}</span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if payment.notes %}
                                            <span class="text-muted" title="{{ payment.notes }}">
                                                {{ payment.notes[:20] }}{% if payment.notes|length > 20 %}...{% endif %}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-muted">
                                        {{ payment.date_added.strftime('%Y-%m-%d %H:%M') }}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('edit_payment', payment_id=payment.id) }}" 
                                               class="btn btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger" 
                                                    title="حذف" onclick="confirmDeletePayment({{ payment.id }}, '{{ payment.amount | currency }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- ملخص الدفعات -->
                    <div class="row mt-4">
                        <div class="col-md-4">
                            <div class="text-center p-3 bg-light rounded">
                                <h6 class="text-muted mb-1">عدد الدفعات</h6>
                                <h4 class="text-primary mb-0">{{ total_payments }}</h4>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3 bg-light rounded">
                                <h6 class="text-muted mb-1">إجمالي المدفوع</h6>
                                <h4 class="text-success mb-0">{{ total_paid_amount | currency }}</h4>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center p-3 bg-light rounded">
                                <h6 class="text-muted mb-1">متوسط الدفعة</h6>
                                <h4 class="text-info mb-0">
                                    {% if total_payments > 0 %}
                                        {{ (total_paid_amount / total_payments) | currency }}
                                    {% else %}
                                        0 د.ع
                                    {% endif %}
                                </h4>
                            </div>
                        </div>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-receipt fa-4x text-muted mb-4"></i>
                        <h4 class="text-muted">لا توجد دفعات مسجلة</h4>
                        <p class="text-muted mb-4">ابدأ بتسجيل دفعتك الأولى لهذا الدين</p>
                        {% if debt.remaining_amount > 0 %}
                        <a href="{{ url_for('add_payment', debt_id=debt.id) }}" class="btn btn-success btn-lg">
                            <i class="fas fa-plus me-2"></i>
                            تسجيل دفعة جديدة
                        </a>
                        {% else %}
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle me-2"></i>
                            تم سداد هذا الدين بالكامل!
                        </div>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- نصائح مفيدة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-lightbulb me-2 text-warning"></i>
                    نصائح لإدارة الدفعات
                </h6>
                <div class="row">
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-receipt text-success me-2 mt-1"></i>
                            <div>
                                <strong>احتفظ بالوصولات</strong>
                                <p class="text-muted small mb-0">سجل رقم الوصل لكل دفعة للمراجعة المستقبلية</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-calendar-check text-info me-2 mt-1"></i>
                            <div>
                                <strong>انتظام الدفع</strong>
                                <p class="text-muted small mb-0">التزم بمواعيد الدفع لتجنب الفوائد الإضافية</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-chart-line text-warning me-2 mt-1"></i>
                            <div>
                                <strong>راقب التقدم</strong>
                                <p class="text-muted small mb-0">تابع نسبة السداد لتحفيز نفسك على الاستمرار</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function confirmDeletePayment(paymentId, amount) {
        if (confirm(`هل أنت متأكد من حذف دفعة بمبلغ ${amount}؟\n\nسيتم إعادة هذا المبلغ للمبلغ المتبقي.\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
            // إنشاء نموذج مخفي لإرسال طلب الحذف
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/delete_payment/${paymentId}`;
            document.body.appendChild(form);
            form.submit();
        }
    }
    
    // تحسين عرض الجدول على الأجهزة الصغيرة
    document.addEventListener('DOMContentLoaded', function() {
        if (window.innerWidth < 768) {
            const table = document.querySelector('.table');
            if (table) {
                table.style.fontSize = '0.85rem';
            }
        }
    });
</script>
{% endblock %}

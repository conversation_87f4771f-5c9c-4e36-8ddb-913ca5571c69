{% extends "base.html" %}

{% block title %}المستحقات الشهرية - نظام إدارة الرواتب{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="text-white text-center mb-4">
            <i class="fas fa-calendar-check me-3"></i>
            إدارة المستحقات الشهرية
        </h1>
    </div>
</div>

<!-- إحصائيات المستحقات -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-list fa-2x text-primary mb-3"></i>
                <h4 class="text-primary">{{ total_active_expenses }}</h4>
                <p class="text-muted mb-0">المستحقات النشطة</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-money-bill-wave fa-2x text-warning mb-3"></i>
                <h4 class="text-warning">{{ total_monthly_amount | currency }}</h4>
                <p class="text-muted mb-0">إجمالي المستحقات الشهرية</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-check-circle fa-2x text-success mb-3"></i>
                <h4 class="text-success">{{ paid_this_month }}</h4>
                <p class="text-muted mb-0">مدفوع هذا الشهر</p>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-exclamation-triangle fa-2x text-danger mb-3"></i>
                <h4 class="text-danger">{{ pending_this_month }}</h4>
                <p class="text-muted mb-0">معلق هذا الشهر</p>
            </div>
        </div>
    </div>
</div>

<!-- أزرار العمليات -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calendar-check me-2 text-primary"></i>
                        إدارة المستحقات
                    </h5>
                    <div>
                        <a href="{{ url_for('add_expense') }}" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>
                            إضافة مستحق جديد
                        </a>
                        <a href="{{ url_for('index') }}" class="btn btn-outline-primary">
                            <i class="fas fa-home me-2"></i>
                            العودة للرئيسية
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول المستحقات -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-table me-2"></i>
                    قائمة المستحقات ({{ expenses|length }} مستحق)
                </h5>
            </div>
            <div class="card-body">
                {% if expenses %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>اسم المستحق</th>
                                    <th>النوع</th>
                                    <th>المبلغ الشهري</th>
                                    <th>يوم الاستحقاق</th>
                                    <th>الجهة المستحقة</th>
                                    <th>حالة الدفع</th>
                                    <th>الحالة</th>
                                    <th>العمليات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for expense in expenses %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>
                                        <strong>{{ expense.name }}</strong>
                                        {% if expense.notes %}
                                            <br><small class="text-muted">{{ expense.notes[:30] }}{% if expense.notes|length > 30 %}...{% endif %}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ expense.category }}</span>
                                    </td>
                                    <td class="text-warning fw-bold">
                                        {{ expense.amount | currency }}
                                    </td>
                                    <td class="text-center">
                                        {% if expense.due_date %}
                                            <span class="badge bg-secondary">{{ expense.due_date }}</span>
                                        {% else %}
                                            <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if expense.payee %}
                                            {{ expense.payee }}
                                        {% else %}
                                            <span class="text-muted">غير محدد</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if expense.payment_status == 'paid' %}
                                            <span class="badge bg-success">مدفوع</span>
                                        {% elif expense.payment_status == 'overdue' %}
                                            <span class="badge bg-danger">متأخر</span>
                                        {% elif expense.payment_status == 'pending' %}
                                            <span class="badge bg-warning">معلق</span>
                                        {% else %}
                                            <span class="badge bg-secondary">غير نشط</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if expense.is_active %}
                                            <span class="badge bg-success">نشط</span>
                                        {% else %}
                                            <span class="badge bg-secondary">متوقف</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('edit_expense', expense_id=expense.id) }}" 
                                               class="btn btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger" 
                                                    title="حذف" onclick="confirmDelete({{ expense.id }}, '{{ expense.name }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- ملخص المستحقات -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">
                                        <i class="fas fa-chart-pie me-2 text-primary"></i>
                                        ملخص المستحقات الشهرية
                                    </h6>
                                    <div class="row text-center">
                                        <div class="col-md-3">
                                            <h6 class="text-muted">النشطة</h6>
                                            <h5 class="text-primary">{{ total_active_expenses }}</h5>
                                        </div>
                                        <div class="col-md-3">
                                            <h6 class="text-muted">الإجمالي الشهري</h6>
                                            <h5 class="text-warning">{{ total_monthly_amount | currency }}</h5>
                                        </div>
                                        <div class="col-md-3">
                                            <h6 class="text-muted">مدفوع هذا الشهر</h6>
                                            <h5 class="text-success">{{ paid_this_month }}</h5>
                                        </div>
                                        <div class="col-md-3">
                                            <h6 class="text-muted">معلق</h6>
                                            <h5 class="text-danger">{{ pending_this_month }}</h5>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-calendar-check fa-4x text-muted mb-4"></i>
                        <h4 class="text-muted">لا توجد مستحقات مسجلة</h4>
                        <p class="text-muted mb-4">ابدأ بإضافة مستحق شهري جديد لتتمكن من تتبع التزاماتك المالية</p>
                        <a href="{{ url_for('add_expense') }}" class="btn btn-primary btn-lg">
                            <i class="fas fa-plus me-2"></i>
                            إضافة مستحق جديد
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- نصائح مفيدة -->
{% if expenses %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-lightbulb me-2 text-warning"></i>
                    نصائح لإدارة المستحقات
                </h6>
                <div class="row">
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-calendar-alt text-success me-2 mt-1"></i>
                            <div>
                                <strong>انتظام الدفع</strong>
                                <p class="text-muted small mb-0">ادفع المستحقات في مواعيدها لتجنب الغرامات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-calculator text-info me-2 mt-1"></i>
                            <div>
                                <strong>راقب الميزانية</strong>
                                <p class="text-muted small mb-0">تأكد من أن المستحقات لا تتجاوز دخلك الشهري</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-bell text-warning me-2 mt-1"></i>
                            <div>
                                <strong>ضع تذكيرات</strong>
                                <p class="text-muted small mb-0">استخدم التقويم لتذكيرك بمواعيد الاستحقاق</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    function confirmDelete(expenseId, expenseName) {
        if (confirm(`هل أنت متأكد من حذف المستحق "${expenseName}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
            // إنشاء نموذج مخفي لإرسال طلب الحذف
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/delete_expense/${expenseId}`;
            document.body.appendChild(form);
            form.submit();
        }
    }
    
    // تحسين عرض الجدول على الأجهزة الصغيرة
    document.addEventListener('DOMContentLoaded', function() {
        if (window.innerWidth < 768) {
            const table = document.querySelector('.table');
            if (table) {
                table.style.fontSize = '0.85rem';
            }
        }
    });
</script>
{% endblock %}

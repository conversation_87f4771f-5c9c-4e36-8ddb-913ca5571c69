{% extends "base.html" %}

{% block title %}الوصول السريع - نظام إدارة الرواتب{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="text-white text-center mb-4">
            <i class="fas fa-rocket me-3"></i>
            الوصول السريع للديون والدفعات
        </h1>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-link me-2"></i>
                    روابط مباشرة للديون
                </h5>
            </div>
            <div class="card-body">
                {% if debts %}
                    <div class="row">
                        {% for debt in debts %}
                        <div class="col-md-6 mb-3">
                            <div class="card border-info">
                                <div class="card-body">
                                    <h6 class="card-title text-primary">{{ debt.creditor_name }}</h6>
                                    <p class="card-text">
                                        <span class="badge bg-info">{{ debt.debt_type }}</span><br>
                                        <small class="text-muted">المبلغ المتبقي: {{ debt.remaining_amount | currency }}</small>
                                    </p>
                                    <div class="d-grid gap-2">
                                        <a href="{{ url_for('debt_details', debt_id=debt.id) }}" 
                                           class="btn btn-primary">
                                            <i class="fas fa-eye me-2"></i>
                                            عرض التفاصيل والدفعات
                                        </a>
                                        <a href="{{ url_for('add_payment', debt_id=debt.id) }}" 
                                           class="btn btn-success">
                                            <i class="fas fa-plus me-2"></i>
                                            إضافة دفعة جديدة
                                        </a>
                                    </div>
                                    <hr>
                                    <small class="text-muted">
                                        <strong>روابط مباشرة:</strong><br>
                                        <code>/debt_details/{{ debt.id }}</code><br>
                                        <code>/add_payment/{{ debt.id }}</code>
                                    </small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                        <h4 class="text-muted">لا توجد ديون</h4>
                        <p class="text-muted">أضف دين جديد أولاً</p>
                        <a href="{{ url_for('add_debt') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            إضافة دين جديد
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- اختبار الروابط -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-vial me-2"></i>
                    اختبار الروابط
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>الروابط العامة:</h6>
                        <ul class="list-group">
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                الصفحة الرئيسية
                                <a href="{{ url_for('index') }}" class="btn btn-sm btn-outline-primary">زيارة</a>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                جميع الديون
                                <a href="{{ url_for('debts') }}" class="btn btn-sm btn-outline-primary">زيارة</a>
                            </li>
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                إضافة دين جديد
                                <a href="{{ url_for('add_debt') }}" class="btn btn-sm btn-outline-success">زيارة</a>
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>روابط الديون المحددة:</h6>
                        {% if debts %}
                            <ul class="list-group">
                                {% for debt in debts[:3] %}
                                <li class="list-group-item">
                                    <strong>{{ debt.creditor_name }}</strong><br>
                                    <small>
                                        <a href="{{ url_for('debt_details', debt_id=debt.id) }}" class="text-primary">التفاصيل</a> | 
                                        <a href="{{ url_for('add_payment', debt_id=debt.id) }}" class="text-success">إضافة دفعة</a>
                                    </small>
                                </li>
                                {% endfor %}
                            </ul>
                        {% else %}
                            <p class="text-muted">لا توجد ديون للاختبار</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

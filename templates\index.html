{% extends "base.html" %}

{% block title %}الصفحة الرئيسية - نظام إدارة الرواتب{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="text-white text-center mb-4">
            <i class="fas fa-chart-line me-3"></i>
            لوحة التحكم المالية الشاملة
        </h1>
    </div>
</div>

<!-- الإحصائيات المالية الشاملة -->
<div class="row mb-4">
    <div class="col-md-2 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-money-bill-wave fa-2x text-success mb-3"></i>
                <h4 class="text-success">{{ total_income | currency }}</h4>
                <p class="text-muted mb-0">إجمالي الدخل</p>
            </div>
        </div>
    </div>

    <div class="col-md-2 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-calendar-check fa-2x text-warning mb-3"></i>
                <h4 class="text-warning">{{ total_monthly_expenses | currency }}</h4>
                <p class="text-muted mb-0">المستحقات الشهرية</p>
            </div>
        </div>
    </div>

    <div class="col-md-2 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-calculator fa-2x
                    {% if financial_status == 'good' %}text-success
                    {% elif financial_status == 'warning' %}text-warning
                    {% else %}text-danger{% endif %} mb-3"></i>
                <h4 class="
                    {% if financial_status == 'good' %}text-success
                    {% elif financial_status == 'warning' %}text-warning
                    {% else %}text-danger{% endif %}">{{ net_salary | currency }}</h4>
                <p class="text-muted mb-0">الراتب الصافي</p>
            </div>
        </div>
    </div>

    <div class="col-md-2 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-credit-card fa-2x text-info mb-3"></i>
                <h4 class="text-info">{{ monthly_debt_payments | currency }}</h4>
                <p class="text-muted mb-0">أقساط الديون</p>
            </div>
        </div>
    </div>

    <div class="col-md-2 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-piggy-bank fa-2x
                    {% if remaining_for_expenses >= 0 %}text-success{% else %}text-danger{% endif %} mb-3"></i>
                <h4 class="
                    {% if remaining_for_expenses >= 0 %}text-success{% else %}text-danger{% endif %}">{{ remaining_for_expenses | currency }}</h4>
                <p class="text-muted mb-0">المتبقي للمصاريف</p>
            </div>
        </div>
    </div>

    <div class="col-md-2 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-chart-pie fa-2x text-primary mb-3"></i>
                <h4 class="text-primary">{{ total_expenses }}</h4>
                <p class="text-muted mb-0">المستحقات النشطة</p>
            </div>
        </div>
    </div>
</div>

<!-- الراتب الحالي -->
{% if current_salary %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-star me-2"></i>
                    راتب الشهر الحالي ({{ current_salary.month }}/{{ current_salary.year }})
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <h6 class="text-muted">الراتب الأول</h6>
                        <h4 class="text-success">{{ current_salary.first_salary | currency }}</h4>
                    </div>
                    <div class="col-md-3">
                        <h6 class="text-muted">الراتب الثاني</h6>
                        <h4 class="text-info">{{ current_salary.second_salary | currency }}</h4>
                    </div>
                    <div class="col-md-3">
                        <h6 class="text-muted">المجموع</h6>
                        <h4 class="text-primary">{{ current_salary.total_salary | currency }}</h4>
                    </div>
                    <div class="col-md-3">
                        <h6 class="text-muted">العمليات</h6>
                        <a href="{{ url_for('edit_salary', salary_id=current_salary.id) }}" class="btn btn-warning btn-sm">
                            <i class="fas fa-edit me-1"></i>
                            تعديل
                        </a>
                    </div>
                </div>
                {% if current_salary.notes %}
                <hr>
                <div class="row">
                    <div class="col-12">
                        <h6 class="text-muted">الملاحظات</h6>
                        <p class="mb-0">{{ current_salary.notes }}</p>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% else %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-warning">
            <div class="card-body text-center">
                <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                <h5 class="text-warning">لم يتم تسجيل راتب هذا الشهر</h5>
                <p class="text-muted">يمكنك إضافة راتب الشهر الحالي الآن</p>
                <a href="{{ url_for('add_salary') }}" class="btn btn-warning">
                    <i class="fas fa-plus me-2"></i>
                    إضافة راتب الشهر الحالي
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- آخر الرواتب -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history me-2"></i>
                    آخر الرواتب المسجلة
                </h5>
            </div>
            <div class="card-body">
                {% if recent_salaries %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>الشهر/السنة</th>
                                    <th>الراتب الأول</th>
                                    <th>الراتب الثاني</th>
                                    <th>المجموع</th>
                                    <th>تاريخ الإضافة</th>
                                    <th>العمليات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for salary in recent_salaries %}
                                <tr>
                                    <td>
                                        <span class="badge bg-primary">{{ salary.month }}/{{ salary.year }}</span>
                                    </td>
                                    <td class="text-success fw-bold">{{ salary.first_salary | currency }}</td>
                                    <td class="text-info fw-bold">{{ salary.second_salary | currency }}</td>
                                    <td class="text-primary fw-bold">{{ salary.total_salary | currency }}</td>
                                    <td class="text-muted">{{ salary.date_added.strftime('%Y-%m-%d') }}</td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('edit_salary', salary_id=salary.id) }}" 
                                               class="btn btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger" 
                                                    title="حذف" onclick="confirmDelete({{ salary.id }}, '{{ salary.month }}/{{ salary.year }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="text-center mt-3">
                        <a href="{{ url_for('salaries') }}" class="btn btn-primary">
                            <i class="fas fa-list me-2"></i>
                            عرض جميع الرواتب
                        </a>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">لا توجد رواتب مسجلة بعد</h6>
                        <p class="text-muted">ابدأ بإضافة راتبك الأول</p>
                        <a href="{{ url_for('add_salary') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            إضافة راتب جديد
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    function confirmDelete(salaryId, monthYear) {
        if (confirm(`هل أنت متأكد من حذف راتب ${monthYear}؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
            // إنشاء نموذج مخفي لإرسال طلب الحذف
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/delete_salary/${salaryId}`;
            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
{% endblock %}

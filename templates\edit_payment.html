{% extends "base.html" %}

{% block title %}تعديل الدفعة - {{ debt.creditor_name }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="text-white text-center mb-4">
            <i class="fas fa-edit me-3"></i>
            تعديل دفعة لدين "{{ debt.creditor_name }}"
        </h1>
    </div>
</div>

<!-- معلومات الدين والدفعة الحالية -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الدين
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-12 mb-2">
                        <small class="text-muted">الدائن/الجهة:</small>
                        <p class="mb-1 fw-bold">{{ debt.creditor_name }}</p>
                    </div>
                    <div class="col-12 mb-2">
                        <small class="text-muted">المبلغ المتبقي الحالي:</small>
                        <p class="mb-1 text-danger fw-bold">{{ debt.remaining_amount | currency }}</p>
                    </div>
                    <div class="col-12">
                        <small class="text-muted">نسبة السداد:</small>
                        <p class="mb-0 text-primary fw-bold">{{ "%.1f"|format(debt.progress_percentage) }}%</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card border-warning">
            <div class="card-header bg-warning text-dark">
                <h6 class="card-title mb-0">
                    <i class="fas fa-receipt me-2"></i>
                    الدفعة الحالية
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-12 mb-2">
                        <small class="text-muted">المبلغ الحالي:</small>
                        <p class="mb-1 text-success fw-bold">{{ payment.amount | currency }}</p>
                    </div>
                    <div class="col-12 mb-2">
                        <small class="text-muted">تاريخ الدفعة:</small>
                        <p class="mb-1">{{ payment.payment_date.strftime('%Y-%m-%d') }}</p>
                    </div>
                    <div class="col-12">
                        <small class="text-muted">رقم الوصل:</small>
                        <p class="mb-0">{{ payment.receipt_number or 'غير محدد' }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج تعديل الدفعة -->
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>
                    نموذج تعديل الدفعة
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="editPaymentForm">
                    <div class="row">
                        <!-- مبلغ الدفعة -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-money-bill text-success me-1"></i>
                                مبلغ الدفعة (دينار عراقي) *
                            </label>
                            <input type="number" name="amount" class="form-control form-control-lg"
                                   placeholder="أدخل مبلغ الدفعة" required min="1" step="1"
                                   value="{{ payment.amount|int }}">
                            <div class="form-text">
                                الحد الأقصى المتاح: {{ (debt.remaining_amount + payment.amount) | currency }}
                            </div>
                        </div>
                        
                        <!-- تاريخ الدفعة -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-calendar text-primary me-1"></i>
                                تاريخ الدفعة *
                            </label>
                            <input type="date" name="payment_date" class="form-control form-control-lg" required
                                   value="{{ payment.payment_date.strftime('%Y-%m-%d') }}">
                            <div class="form-text">تاريخ دفع المبلغ الفعلي</div>
                        </div>
                    </div>
                    
                    <!-- رقم الوصل -->
                    <div class="mb-3">
                        <label class="form-label fw-bold">
                            <i class="fas fa-receipt text-info me-1"></i>
                            رقم الوصل/الإيصال
                        </label>
                        <input type="text" name="receipt_number" class="form-control form-control-lg" 
                               placeholder="أدخل رقم الوصل أو الإيصال (اختياري)"
                               value="{{ payment.receipt_number or '' }}">
                        <div class="form-text">رقم الوصل المرجعي للدفعة (اختياري)</div>
                    </div>
                    
                    <!-- الملاحظات -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">
                            <i class="fas fa-sticky-note text-secondary me-1"></i>
                            ملاحظات إضافية
                        </label>
                        <textarea name="notes" class="form-control" rows="3" 
                                  placeholder="أضف أي ملاحظات مهمة حول هذه الدفعة...">{{ payment.notes or '' }}</textarea>
                        <div class="form-text">يمكنك إضافة تفاصيل إضافية عن الدفعة</div>
                    </div>
                    
                    <!-- معاينة التغييرات -->
                    <div class="mb-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="text-muted mb-3">معاينة التغييرات</h6>
                                <div class="row">
                                    <div class="col-md-4 text-center">
                                        <h6 class="text-muted">المبلغ الجديد</h6>
                                        <h4 class="text-success mb-0" id="newAmountDisplay">{{ payment.amount | currency }}</h4>
                                    </div>
                                    <div class="col-md-4 text-center">
                                        <h6 class="text-muted">المتبقي بعد التعديل</h6>
                                        <h4 class="text-danger mb-0" id="newRemainingDisplay">{{ debt.remaining_amount | currency }}</h4>
                                    </div>
                                    <div class="col-md-4 text-center">
                                        <h6 class="text-muted">نسبة السداد الجديدة</h6>
                                        <h4 class="text-primary mb-0" id="newProgressDisplay">{{ "%.1f"|format(debt.progress_percentage) }}%</h4>
                                    </div>
                                </div>
                                
                                <!-- شريط التقدم الجديد -->
                                <div class="mt-3">
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar bg-success" role="progressbar" 
                                             style="width: {{ debt.progress_percentage }}%" id="newProgressBar">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- معلومات إضافية -->
                    <div class="mb-4">
                        <div class="card border-secondary">
                            <div class="card-body">
                                <h6 class="card-title text-secondary">
                                    <i class="fas fa-info-circle me-2"></i>
                                    معلومات الدفعة
                                </h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <small class="text-muted">تاريخ التسجيل الأصلي:</small>
                                        <p class="mb-1">{{ payment.date_added.strftime('%Y-%m-%d %H:%M') }}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted">رقم الدفعة:</small>
                                        <p class="mb-1">#{{ payment.id }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- أزرار العمليات -->
                    <div class="row">
                        <div class="col-md-4 mb-2">
                            <button type="submit" class="btn btn-warning btn-lg w-100">
                                <i class="fas fa-save me-2"></i>
                                حفظ التعديلات
                            </button>
                        </div>
                        <div class="col-md-4 mb-2">
                            <a href="{{ url_for('debt_details', debt_id=debt.id) }}" class="btn btn-outline-secondary btn-lg w-100">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                        <div class="col-md-4 mb-2">
                            <button type="button" class="btn btn-outline-danger btn-lg w-100" 
                                    onclick="confirmDeletePayment({{ payment.id }}, '{{ payment.amount | currency }}')">
                                <i class="fas fa-trash me-2"></i>
                                حذف الدفعة
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- نصائح مفيدة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-lightbulb me-2 text-warning"></i>
                    نصائح للتعديل
                </h6>
                <div class="row">
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-exclamation-triangle text-warning me-2 mt-1"></i>
                            <div>
                                <strong>تحقق من المبلغ</strong>
                                <p class="text-muted small mb-0">تأكد من صحة المبلغ الجديد قبل الحفظ</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-calculator text-info me-2 mt-1"></i>
                            <div>
                                <strong>إعادة الحساب</strong>
                                <p class="text-muted small mb-0">سيتم إعادة حساب المبلغ المتبقي تلقائياً</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-save text-success me-2 mt-1"></i>
                            <div>
                                <strong>احفظ التغييرات</strong>
                                <p class="text-muted small mb-0">لا تنس حفظ التعديلات بعد الانتهاء</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // عناصر النموذج
        const amountInput = document.querySelector('input[name="amount"]');
        
        // عناصر العرض
        const newAmountDisplay = document.getElementById('newAmountDisplay');
        const newRemainingDisplay = document.getElementById('newRemainingDisplay');
        const newProgressDisplay = document.getElementById('newProgressDisplay');
        const newProgressBar = document.getElementById('newProgressBar');
        
        // بيانات الدين والدفعة
        const totalAmount = {{ debt.total_amount }};
        const currentRemaining = {{ debt.remaining_amount }};
        const originalPaymentAmount = {{ payment.amount }};
        
        // تحديث العرض
        function updateDisplay() {
            const newPaymentAmount = parseFloat(amountInput.value) || 0;
            const amountDifference = newPaymentAmount - originalPaymentAmount;
            const newRemaining = Math.max(0, currentRemaining - amountDifference);
            const newProgress = ((totalAmount - newRemaining) / totalAmount) * 100;
            
            // عرض المبلغ الجديد
            newAmountDisplay.textContent = newPaymentAmount.toLocaleString('ar-IQ') + ' د.ع';
            
            // عرض المتبقي الجديد
            newRemainingDisplay.textContent = newRemaining.toLocaleString('ar-IQ') + ' د.ع';
            
            // عرض نسبة السداد الجديدة
            newProgressDisplay.textContent = newProgress.toFixed(1) + '%';
            
            // تحديث شريط التقدم
            newProgressBar.style.width = newProgress + '%';
            
            // تغيير لون شريط التقدم
            if (newProgress >= 100) {
                newProgressBar.className = 'progress-bar bg-success';
            } else if (newProgress >= 75) {
                newProgressBar.className = 'progress-bar bg-success';
            } else if (newProgress >= 50) {
                newProgressBar.className = 'progress-bar bg-warning';
            } else if (newProgress >= 25) {
                newProgressBar.className = 'progress-bar bg-info';
            } else {
                newProgressBar.className = 'progress-bar bg-danger';
            }
        }
        
        // ربط الأحداث
        amountInput.addEventListener('input', updateDisplay);
        
        // تحسين النموذج
        const form = document.getElementById('editPaymentForm');
        form.addEventListener('submit', function(e) {
            const newPaymentAmount = parseFloat(amountInput.value) || 0;
            const maxAvailable = currentRemaining + originalPaymentAmount;
            
            if (newPaymentAmount <= 0) {
                e.preventDefault();
                alert('يرجى إدخال مبلغ الدفعة');
                amountInput.focus();
                return false;
            }
            
            if (newPaymentAmount > maxAvailable) {
                e.preventDefault();
                alert(`المبلغ المتاح للدفع هو ${maxAvailable.toLocaleString('ar-IQ')} د.ع فقط`);
                amountInput.focus();
                return false;
            }
            
            // تأكيد التعديل
            if (!confirm('هل أنت متأكد من حفظ التعديلات؟')) {
                e.preventDefault();
                return false;
            }
        });
        
        // تنسيق المدخلات الرقمية
        amountInput.addEventListener('input', function() {
            // إزالة الأحرف غير الرقمية
            this.value = this.value.replace(/[^0-9]/g, '');
        });
    });
    
    function confirmDeletePayment(paymentId, amount) {
        if (confirm(`هل أنت متأكد من حذف دفعة بمبلغ ${amount}؟\n\nسيتم إعادة هذا المبلغ للمبلغ المتبقي.\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
            // إنشاء نموذج مخفي لإرسال طلب الحذف
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/delete_payment/${paymentId}`;
            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
{% endblock %}

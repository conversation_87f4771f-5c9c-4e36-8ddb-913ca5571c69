{% extends "base.html" %}

{% block title %}تعديل المستحق - نظام إدارة الرواتب{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="text-white text-center mb-4">
            <i class="fas fa-edit me-3"></i>
            تعديل المستحق "{{ expense.name }}"
        </h1>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-10">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>
                    نموذج تعديل المستحق
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="editExpenseForm">
                    <div class="row">
                        <!-- اسم المستحق -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-tag text-primary me-1"></i>
                                اسم المستحق *
                            </label>
                            <input type="text" name="name" class="form-control form-control-lg" 
                                   placeholder="مثال: إيجار المنزل، اشتراك الإنترنت" required
                                   value="{{ expense.name }}">
                            <div class="form-text">أدخل اسماً وصفياً للمستحق</div>
                        </div>
                        
                        <!-- نوع المستحق -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-list text-info me-1"></i>
                                نوع المستحق *
                            </label>
                            <select name="category" class="form-select form-select-lg" required>
                                <option value="">اختر نوع المستحق</option>
                                <option value="إيجار" {% if expense.category == 'إيجار' %}selected{% endif %}>🏠 إيجار المنزل</option>
                                <option value="كهرباء" {% if expense.category == 'كهرباء' %}selected{% endif %}>⚡ اشتراك المولد الكهربائي</option>
                                <option value="إنترنت" {% if expense.category == 'إنترنت' %}selected{% endif %}>🌐 اشتراك الإنترنت</option>
                                <option value="هاتف" {% if expense.category == 'هاتف' %}selected{% endif %}>📱 فاتورة الهاتف</option>
                                <option value="ماء" {% if expense.category == 'ماء' %}selected{% endif %}>💧 فاتورة الماء</option>
                                <option value="غاز" {% if expense.category == 'غاز' %}selected{% endif %}>🔥 الغاز</option>
                                <option value="وقود" {% if expense.category == 'وقود' %}selected{% endif %}>🚗 وقود السيارة</option>
                                <option value="بقالة" {% if expense.category == 'بقالة' %}selected{% endif %}>🛒 مصاريف البقالة</option>
                                <option value="أدوية" {% if expense.category == 'أدوية' %}selected{% endif %}>💊 الأدوية</option>
                                <option value="تعليم" {% if expense.category == 'تعليم' %}selected{% endif %}>📚 رسوم المدرسة</option>
                                <option value="تأمين" {% if expense.category == 'تأمين' %}selected{% endif %}>🛡️ التأمين</option>
                                <option value="أخرى" {% if expense.category == 'أخرى' %}selected{% endif %}>🎯 أخرى</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- المبلغ الشهري -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-money-bill-wave text-warning me-1"></i>
                                المبلغ الشهري (دينار عراقي) *
                            </label>
                            <input type="number" name="amount" class="form-control form-control-lg" 
                                   placeholder="أدخل المبلغ الشهري" required min="0" step="1"
                                   value="{{ expense.amount|int }}">
                            <div class="form-text">المبلغ المطلوب دفعه شهرياً</div>
                        </div>
                        
                        <!-- يوم الاستحقاق -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-calendar-alt text-success me-1"></i>
                                يوم الاستحقاق الشهري
                            </label>
                            <input type="number" name="due_date" class="form-control form-control-lg" 
                                   placeholder="مثال: 15 (لليوم 15 من كل شهر)" min="1" max="31"
                                   value="{{ expense.due_date if expense.due_date else '' }}">
                            <div class="form-text">اليوم من الشهر المطلوب فيه الدفع (اختياري)</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- الجهة المستحقة -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-building text-info me-1"></i>
                                الجهة المستحقة
                            </label>
                            <input type="text" name="payee" class="form-control form-control-lg" 
                                   placeholder="مثال: شركة الكهرباء، المالك"
                                   value="{{ expense.payee or '' }}">
                            <div class="form-text">اسم الشركة أو الشخص المستحق للدفع (اختياري)</div>
                        </div>
                        
                        <!-- معلومات الاتصال -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-phone text-secondary me-1"></i>
                                معلومات الاتصال
                            </label>
                            <input type="text" name="contact_info" class="form-control form-control-lg" 
                                   placeholder="رقم الهاتف، العنوان، إلخ"
                                   value="{{ expense.contact_info or '' }}">
                            <div class="form-text">رقم هاتف أو عنوان للتواصل (اختياري)</div>
                        </div>
                    </div>
                    
                    <!-- الملاحظات -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">
                            <i class="fas fa-sticky-note text-secondary me-1"></i>
                            ملاحظات إضافية
                        </label>
                        <textarea name="notes" class="form-control" rows="3" 
                                  placeholder="أضف أي ملاحظات مهمة حول هذا المستحق...">{{ expense.notes or '' }}</textarea>
                        <div class="form-text">يمكنك إضافة تفاصيل إضافية مثل شروط الدفع أو تذكيرات</div>
                    </div>
                    
                    <!-- إعدادات المستحق -->
                    <div class="mb-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-cog me-2 text-primary"></i>
                                    إعدادات المستحق
                                </h6>
                                <div class="form-check form-switch mb-2">
                                    <input class="form-check-input" type="checkbox" name="is_active" id="isActive" 
                                           {% if expense.is_active %}checked{% endif %}>
                                    <label class="form-check-label" for="isActive">
                                        <strong>المستحق نشط</strong>
                                        <br><small class="text-muted">إذا كان نشطاً سيتم تضمينه في حسابات الراتب الصافي</small>
                                    </label>
                                </div>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" name="auto_deduct" id="autoDeduct" 
                                           {% if expense.auto_deduct %}checked{% endif %}>
                                    <label class="form-check-label" for="autoDeduct">
                                        <strong>خصم تلقائي من الراتب</strong>
                                        <br><small class="text-muted">سيتم خصم هذا المستحق تلقائياً من حساب الراتب الصافي</small>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- معلومات المستحق الحالية -->
                    <div class="mb-4">
                        <div class="card border-info">
                            <div class="card-body">
                                <h6 class="card-title text-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    معلومات المستحق الحالية
                                </h6>
                                <div class="row">
                                    <div class="col-md-3">
                                        <small class="text-muted">المبلغ الحالي:</small>
                                        <p class="mb-1 text-warning fw-bold">{{ expense.amount|currency }}</p>
                                    </div>
                                    <div class="col-md-3">
                                        <small class="text-muted">النوع:</small>
                                        <p class="mb-1"><span class="badge bg-info">{{ expense.category }}</span></p>
                                    </div>
                                    <div class="col-md-3">
                                        <small class="text-muted">الحالة:</small>
                                        <p class="mb-1">
                                            {% if expense.is_active %}
                                                <span class="badge bg-success">نشط</span>
                                            {% else %}
                                                <span class="badge bg-secondary">متوقف</span>
                                            {% endif %}
                                        </p>
                                    </div>
                                    <div class="col-md-3">
                                        <small class="text-muted">تاريخ الإضافة:</small>
                                        <p class="mb-1">{{ expense.date_added.strftime('%Y-%m-%d') }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- معاينة التعديلات -->
                    <div class="mb-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="text-muted mb-3">معاينة التعديلات</h6>
                                <div class="row">
                                    <div class="col-md-4 text-center">
                                        <h6 class="text-muted">المبلغ الشهري الجديد</h6>
                                        <h4 class="text-warning mb-0" id="monthlyAmountDisplay">{{ expense.amount|currency }}</h4>
                                    </div>
                                    <div class="col-md-4 text-center">
                                        <h6 class="text-muted">المبلغ السنوي</h6>
                                        <h4 class="text-info mb-0" id="yearlyAmountDisplay">{{ (expense.amount * 12)|currency }}</h4>
                                    </div>
                                    <div class="col-md-4 text-center">
                                        <h6 class="text-muted">يوم الاستحقاق</h6>
                                        <h4 class="text-success mb-0" id="dueDateDisplay">
                                            {% if expense.due_date %}{{ expense.due_date }} من كل شهر{% else %}غير محدد{% endif %}
                                        </h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- أزرار العمليات -->
                    <div class="row">
                        <div class="col-md-4 mb-2">
                            <button type="submit" class="btn btn-warning btn-lg w-100">
                                <i class="fas fa-save me-2"></i>
                                حفظ التعديلات
                            </button>
                        </div>
                        <div class="col-md-4 mb-2">
                            <a href="{{ url_for('expenses') }}" class="btn btn-outline-secondary btn-lg w-100">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                        <div class="col-md-4 mb-2">
                            <button type="button" class="btn btn-outline-danger btn-lg w-100" 
                                    onclick="confirmDelete({{ expense.id }}, '{{ expense.name }}')">
                                <i class="fas fa-trash me-2"></i>
                                حذف المستحق
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- نصائح مفيدة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-lightbulb me-2 text-warning"></i>
                    نصائح للتعديل
                </h6>
                <div class="row">
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-exclamation-triangle text-warning me-2 mt-1"></i>
                            <div>
                                <strong>تحقق من البيانات</strong>
                                <p class="text-muted small mb-0">تأكد من صحة المبالغ والتواريخ قبل الحفظ</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-calculator text-info me-2 mt-1"></i>
                            <div>
                                <strong>تأثير على الراتب</strong>
                                <p class="text-muted small mb-0">التعديل سيؤثر على حساب الراتب الصافي</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-save text-success me-2 mt-1"></i>
                            <div>
                                <strong>احفظ التغييرات</strong>
                                <p class="text-muted small mb-0">لا تنس حفظ التعديلات بعد الانتهاء</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // عناصر النموذج
        const amountInput = document.querySelector('input[name="amount"]');
        const dueDateInput = document.querySelector('input[name="due_date"]');
        
        // عناصر العرض
        const monthlyAmountDisplay = document.getElementById('monthlyAmountDisplay');
        const yearlyAmountDisplay = document.getElementById('yearlyAmountDisplay');
        const dueDateDisplay = document.getElementById('dueDateDisplay');
        
        // تحديث العرض
        function updateDisplay() {
            const monthlyAmount = parseFloat(amountInput.value) || 0;
            const yearlyAmount = monthlyAmount * 12;
            const dueDate = parseInt(dueDateInput.value) || 0;
            
            // عرض المبلغ الشهري
            monthlyAmountDisplay.textContent = monthlyAmount.toLocaleString('ar-IQ') + ' د.ع';
            
            // عرض المبلغ السنوي
            yearlyAmountDisplay.textContent = yearlyAmount.toLocaleString('ar-IQ') + ' د.ع';
            
            // عرض يوم الاستحقاق
            if (dueDate > 0 && dueDate <= 31) {
                dueDateDisplay.textContent = dueDate + ' من كل شهر';
            } else {
                dueDateDisplay.textContent = 'غير محدد';
            }
        }
        
        // ربط الأحداث
        amountInput.addEventListener('input', updateDisplay);
        dueDateInput.addEventListener('input', updateDisplay);
        
        // تحسين النموذج
        const form = document.getElementById('editExpenseForm');
        form.addEventListener('submit', function(e) {
            const name = document.querySelector('input[name="name"]').value.trim();
            const category = document.querySelector('select[name="category"]').value;
            const amount = parseFloat(amountInput.value) || 0;
            
            if (!name) {
                e.preventDefault();
                alert('يرجى إدخال اسم المستحق');
                document.querySelector('input[name="name"]').focus();
                return false;
            }
            
            if (!category) {
                e.preventDefault();
                alert('يرجى اختيار نوع المستحق');
                document.querySelector('select[name="category"]').focus();
                return false;
            }
            
            if (amount <= 0) {
                e.preventDefault();
                alert('يرجى إدخال مبلغ صحيح');
                amountInput.focus();
                return false;
            }
            
            // تأكيد التعديل
            if (!confirm('هل أنت متأكد من حفظ التعديلات؟')) {
                e.preventDefault();
                return false;
            }
        });
        
        // تنسيق المدخلات الرقمية
        [amountInput, dueDateInput].forEach(input => {
            input.addEventListener('input', function() {
                // إزالة الأحرف غير الرقمية
                this.value = this.value.replace(/[^0-9]/g, '');
            });
        });
    });
    
    function confirmDelete(expenseId, expenseName) {
        if (confirm(`هل أنت متأكد من حذف المستحق "${expenseName}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
            // إنشاء نموذج مخفي لإرسال طلب الحذف
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/delete_expense/${expenseId}`;
            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
{% endblock %}

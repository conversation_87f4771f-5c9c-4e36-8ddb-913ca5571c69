#!/usr/bin/env python3
"""
اختبار الديون والدفعات
Test Debts and Payments
"""

from app import app, db, Debt, DebtPayment
from datetime import datetime, date

def test_debts():
    """اختبار الديون"""
    print("🧪 اختبار الديون...")
    
    with app.app_context():
        try:
            # عد الديون
            debt_count = Debt.query.count()
            print(f"📊 عدد الديون: {debt_count}")
            
            if debt_count > 0:
                debts = Debt.query.all()
                print("📋 الديون الموجودة:")
                for debt in debts:
                    print(f"   - {debt.creditor_name} ({debt.debt_type}) - {debt.total_amount:,.0f} د.ع")
                    print(f"     المتبقي: {debt.remaining_amount:,.0f} د.ع")
                    print(f"     نسبة السداد: {debt.progress_percentage:.1f}%")
                    
                    # عد الدفعات لهذا الدين
                    payment_count = DebtPayment.query.filter_by(debt_id=debt.id).count()
                    print(f"     عدد الدفعات: {payment_count}")
                    print()
            else:
                print("❌ لا توجد ديون")
                
                # إنشاء دين تجريبي
                print("🔧 إنشاء دين تجريبي...")
                test_debt = Debt(
                    creditor_name="بنك بغداد",
                    debt_type="سيارة",
                    total_amount=25000000,
                    remaining_amount=25000000,
                    monthly_payment=1250000,
                    installments_count=20,
                    monthly_due_date=15,
                    description="قرض سيارة تجريبي"
                )
                
                db.session.add(test_debt)
                db.session.commit()
                
                print(f"✅ تم إنشاء دين تجريبي: {test_debt.creditor_name}")
                print(f"   ID: {test_debt.id}")
                print(f"   المبلغ: {test_debt.total_amount:,.0f} د.ع")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ: {e}")
            return False

def test_routes():
    """اختبار الروابط"""
    print("🌐 اختبار الروابط...")
    
    with app.test_client() as client:
        try:
            # اختبار صفحة الديون
            response = client.get('/debts')
            print(f"📋 /debts - Status: {response.status_code}")
            
            # اختبار صفحة إضافة دين
            response = client.get('/add_debt')
            print(f"➕ /add_debt - Status: {response.status_code}")
            
            # اختبار تفاصيل دين (إذا وجد)
            with app.app_context():
                first_debt = Debt.query.first()
                if first_debt:
                    response = client.get(f'/debt_details/{first_debt.id}')
                    print(f"👁️ /debt_details/{first_debt.id} - Status: {response.status_code}")
                    
                    response = client.get(f'/add_payment/{first_debt.id}')
                    print(f"💳 /add_payment/{first_debt.id} - Status: {response.status_code}")
                else:
                    print("❌ لا توجد ديون لاختبار التفاصيل")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في اختبار الروابط: {e}")
            return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🧪 اختبار نظام الديون والدفعات")
    print("=" * 60)
    print()
    
    # اختبار الديون
    debts_ok = test_debts()
    print()
    
    # اختبار الروابط
    routes_ok = test_routes()
    print()
    
    if debts_ok and routes_ok:
        print("🎉 جميع الاختبارات نجحت!")
        print()
        print("🌐 الروابط المتاحة:")
        print("   📋 الديون: http://localhost:5000/debts")
        print("   ➕ إضافة دين: http://localhost:5000/add_debt")
        
        with app.app_context():
            first_debt = Debt.query.first()
            if first_debt:
                print(f"   👁️ تفاصيل الدين: http://localhost:5000/debt_details/{first_debt.id}")
                print(f"   💳 إضافة دفعة: http://localhost:5000/add_payment/{first_debt.id}")
    else:
        print("❌ فشل في بعض الاختبارات")
    
    print("=" * 60)

if __name__ == "__main__":
    main()

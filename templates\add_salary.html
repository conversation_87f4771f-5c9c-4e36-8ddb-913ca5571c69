{% extends "base.html" %}

{% block title %}إضافة راتب جديد - نظام إدارة الرواتب{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="text-white text-center mb-4">
            <i class="fas fa-plus me-3"></i>
            إضافة راتب جديد
        </h1>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i>
                    نموذج إضافة راتب جديد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="salaryForm">
                    <div class="row">
                        <!-- الراتب الأول -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-money-bill text-success me-1"></i>
                                الراتب الأول (دينار عراقي) *
                            </label>
                            <input type="number" name="first_salary" class="form-control form-control-lg"
                                   placeholder="أدخل مبلغ الراتب الأول" required min="0" step="1">
                            <div class="form-text">الراتب الأساسي أو المصدر الرئيسي للدخل</div>
                        </div>
                        
                        <!-- الراتب الثاني -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-coins text-info me-1"></i>
                                الراتب الثاني (دينار عراقي)
                            </label>
                            <input type="number" name="second_salary" class="form-control form-control-lg"
                                   placeholder="أدخل مبلغ الراتب الثاني (اختياري)" min="0" step="1">
                            <div class="form-text">راتب إضافي أو مصدر دخل ثانوي (اختياري)</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- الشهر -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-calendar text-primary me-1"></i>
                                الشهر *
                            </label>
                            <select name="month" class="form-select form-select-lg" required>
                                <option value="">اختر الشهر</option>
                                <option value="1">يناير</option>
                                <option value="2">فبراير</option>
                                <option value="3">مارس</option>
                                <option value="4">أبريل</option>
                                <option value="5">مايو</option>
                                <option value="6">يونيو</option>
                                <option value="7">يوليو</option>
                                <option value="8">أغسطس</option>
                                <option value="9">سبتمبر</option>
                                <option value="10">أكتوبر</option>
                                <option value="11">نوفمبر</option>
                                <option value="12">ديسمبر</option>
                            </select>
                        </div>
                        
                        <!-- السنة -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-calendar-alt text-warning me-1"></i>
                                السنة *
                            </label>
                            <input type="number" name="year" class="form-control form-control-lg" 
                                   placeholder="أدخل السنة" required min="2020" max="2030" id="yearInput">
                        </div>
                    </div>
                    
                    <!-- الملاحظات -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">
                            <i class="fas fa-sticky-note text-secondary me-1"></i>
                            ملاحظات إضافية
                        </label>
                        <textarea name="notes" class="form-control" rows="4" 
                                  placeholder="أضف أي ملاحظات مهمة مثل المكافآت، الخصومات، أو تفاصيل أخرى..."></textarea>
                        <div class="form-text">يمكنك إضافة تفاصيل إضافية مثل المكافآت أو الخصومات</div>
                    </div>
                    
                    <!-- عرض المجموع -->
                    <div class="mb-4">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="text-muted mb-2">المجموع الإجمالي</h6>
                                <h3 class="text-primary mb-0" id="totalAmount">0 د.ع</h3>
                            </div>
                        </div>
                    </div>
                    
                    <!-- أزرار العمليات -->
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <button type="submit" class="btn btn-success btn-lg w-100">
                                <i class="fas fa-save me-2"></i>
                                حفظ الراتب
                            </button>
                        </div>
                        <div class="col-md-6 mb-2">
                            <a href="{{ url_for('salaries') }}" class="btn btn-outline-secondary btn-lg w-100">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- نصائح مفيدة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-lightbulb me-2 text-warning"></i>
                    نصائح لإضافة الراتب
                </h6>
                <div class="row">
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-check-circle text-success me-2 mt-1"></i>
                            <div>
                                <strong>كن دقيقاً</strong>
                                <p class="text-muted small mb-0">أدخل المبالغ الصحيحة كما هي في كشف الراتب</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-calendar-check text-info me-2 mt-1"></i>
                            <div>
                                <strong>اختر التاريخ الصحيح</strong>
                                <p class="text-muted small mb-0">تأكد من اختيار الشهر والسنة الصحيحين</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-sticky-note text-warning me-2 mt-1"></i>
                            <div>
                                <strong>استخدم الملاحظات</strong>
                                <p class="text-muted small mb-0">أضف تفاصيل مهمة مثل المكافآت أو الخصومات</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تعيين السنة الحالية كقيمة افتراضية
        const yearInput = document.getElementById('yearInput');
        const currentYear = new Date().getFullYear();
        yearInput.value = currentYear;
        
        // تعيين الشهر الحالي كقيمة افتراضية
        const monthSelect = document.querySelector('select[name="month"]');
        const currentMonth = new Date().getMonth() + 1;
        monthSelect.value = currentMonth;
        
        // حساب المجموع تلقائياً
        const firstSalaryInput = document.querySelector('input[name="first_salary"]');
        const secondSalaryInput = document.querySelector('input[name="second_salary"]');
        const totalAmountDisplay = document.getElementById('totalAmount');
        
        function updateTotal() {
            const first = parseFloat(firstSalaryInput.value) || 0;
            const second = parseFloat(secondSalaryInput.value) || 0;
            const total = first + second;
            
            totalAmountDisplay.textContent = total.toLocaleString('ar-IQ') + ' د.ع';
            
            // تغيير لون المجموع حسب القيمة
            if (total > 0) {
                totalAmountDisplay.className = 'text-success mb-0';
            } else {
                totalAmountDisplay.className = 'text-muted mb-0';
            }
        }
        
        // ربط الأحداث
        firstSalaryInput.addEventListener('input', updateTotal);
        secondSalaryInput.addEventListener('input', updateTotal);
        
        // تحسين النموذج
        const form = document.getElementById('salaryForm');
        form.addEventListener('submit', function(e) {
            const firstSalary = parseFloat(firstSalaryInput.value) || 0;
            
            if (firstSalary <= 0) {
                e.preventDefault();
                alert('يرجى إدخال مبلغ الراتب الأول');
                firstSalaryInput.focus();
                return false;
            }
            
            // تأكيد الحفظ
            const total = (parseFloat(firstSalaryInput.value) || 0) + (parseFloat(secondSalaryInput.value) || 0);
            const month = monthSelect.options[monthSelect.selectedIndex].text;
            const year = yearInput.value;
            
            if (!confirm(`هل أنت متأكد من حفظ راتب ${month}/${year} بمبلغ ${total.toLocaleString('ar-IQ')} دينار عراقي؟`)) {
                e.preventDefault();
                return false;
            }
        });
        
        // تنسيق المدخلات الرقمية
        [firstSalaryInput, secondSalaryInput].forEach(input => {
            input.addEventListener('input', function() {
                // إزالة الأحرف غير الرقمية
                this.value = this.value.replace(/[^0-9]/g, '');
            });
        });
    });
</script>
{% endblock %}

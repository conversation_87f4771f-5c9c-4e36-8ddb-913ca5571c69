{% extends "base.html" %}

{% block title %}إضافة مستحق جديد - نظام إدارة الرواتب{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="text-white text-center mb-4">
            <i class="fas fa-plus me-3"></i>
            إضافة مستحق شهري جديد
        </h1>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-10">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-check me-2"></i>
                    نموذج إضافة مستحق شهري
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="expenseForm">
                    <div class="row">
                        <!-- اسم المستحق -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-tag text-primary me-1"></i>
                                اسم المستحق *
                            </label>
                            <input type="text" name="name" class="form-control form-control-lg" 
                                   placeholder="مثال: إيجار المنزل، اشتراك الإنترنت" required>
                            <div class="form-text">أدخل اسماً وصفياً للمستحق</div>
                        </div>
                        
                        <!-- نوع المستحق -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-list text-info me-1"></i>
                                نوع المستحق *
                            </label>
                            <select name="category" class="form-select form-select-lg" required>
                                <option value="">اختر نوع المستحق</option>
                                <option value="إيجار">🏠 إيجار المنزل</option>
                                <option value="كهرباء">⚡ اشتراك المولد الكهربائي</option>
                                <option value="إنترنت">🌐 اشتراك الإنترنت</option>
                                <option value="هاتف">📱 فاتورة الهاتف</option>
                                <option value="ماء">💧 فاتورة الماء</option>
                                <option value="غاز">🔥 الغاز</option>
                                <option value="وقود">🚗 وقود السيارة</option>
                                <option value="بقالة">🛒 مصاريف البقالة</option>
                                <option value="أدوية">💊 الأدوية</option>
                                <option value="تعليم">📚 رسوم المدرسة</option>
                                <option value="تأمين">🛡️ التأمين</option>
                                <option value="أخرى">🎯 أخرى</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- المبلغ الشهري -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-money-bill-wave text-warning me-1"></i>
                                المبلغ الشهري (دينار عراقي) *
                            </label>
                            <input type="number" name="amount" class="form-control form-control-lg" 
                                   placeholder="أدخل المبلغ الشهري" required min="0" step="1">
                            <div class="form-text">المبلغ المطلوب دفعه شهرياً</div>
                        </div>
                        
                        <!-- يوم الاستحقاق -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-calendar-alt text-success me-1"></i>
                                يوم الاستحقاق الشهري
                            </label>
                            <input type="number" name="due_date" class="form-control form-control-lg" 
                                   placeholder="مثال: 15 (لليوم 15 من كل شهر)" min="1" max="31">
                            <div class="form-text">اليوم من الشهر المطلوب فيه الدفع (اختياري)</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- الجهة المستحقة -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-building text-info me-1"></i>
                                الجهة المستحقة
                            </label>
                            <input type="text" name="payee" class="form-control form-control-lg" 
                                   placeholder="مثال: شركة الكهرباء، المالك">
                            <div class="form-text">اسم الشركة أو الشخص المستحق للدفع (اختياري)</div>
                        </div>
                        
                        <!-- معلومات الاتصال -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-phone text-secondary me-1"></i>
                                معلومات الاتصال
                            </label>
                            <input type="text" name="contact_info" class="form-control form-control-lg" 
                                   placeholder="رقم الهاتف، العنوان، إلخ">
                            <div class="form-text">رقم هاتف أو عنوان للتواصل (اختياري)</div>
                        </div>
                    </div>
                    
                    <!-- الملاحظات -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">
                            <i class="fas fa-sticky-note text-secondary me-1"></i>
                            ملاحظات إضافية
                        </label>
                        <textarea name="notes" class="form-control" rows="3" 
                                  placeholder="أضف أي ملاحظات مهمة حول هذا المستحق..."></textarea>
                        <div class="form-text">يمكنك إضافة تفاصيل إضافية مثل شروط الدفع أو تذكيرات</div>
                    </div>
                    
                    <!-- إعدادات المستحق -->
                    <div class="mb-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-cog me-2 text-primary"></i>
                                    إعدادات المستحق
                                </h6>
                                <div class="form-check form-switch mb-2">
                                    <input class="form-check-input" type="checkbox" name="auto_deduct" id="autoDeduct" checked>
                                    <label class="form-check-label" for="autoDeduct">
                                        <strong>خصم تلقائي من الراتب</strong>
                                        <br><small class="text-muted">سيتم خصم هذا المستحق تلقائياً من حساب الراتب الصافي</small>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- معاينة المستحق -->
                    <div class="mb-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="text-muted mb-3">معاينة المستحق</h6>
                                <div class="row">
                                    <div class="col-md-4 text-center">
                                        <h6 class="text-muted">المبلغ الشهري</h6>
                                        <h4 class="text-warning mb-0" id="monthlyAmountDisplay">0 د.ع</h4>
                                    </div>
                                    <div class="col-md-4 text-center">
                                        <h6 class="text-muted">المبلغ السنوي</h6>
                                        <h4 class="text-info mb-0" id="yearlyAmountDisplay">0 د.ع</h4>
                                    </div>
                                    <div class="col-md-4 text-center">
                                        <h6 class="text-muted">يوم الاستحقاق</h6>
                                        <h4 class="text-success mb-0" id="dueDateDisplay">غير محدد</h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- أزرار العمليات -->
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <button type="submit" class="btn btn-success btn-lg w-100">
                                <i class="fas fa-save me-2"></i>
                                حفظ المستحق
                            </button>
                        </div>
                        <div class="col-md-6 mb-2">
                            <a href="{{ url_for('expenses') }}" class="btn btn-outline-secondary btn-lg w-100">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- نصائح مفيدة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-lightbulb me-2 text-warning"></i>
                    نصائح لإضافة المستحق
                </h6>
                <div class="row">
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-check-circle text-success me-2 mt-1"></i>
                            <div>
                                <strong>كن دقيقاً</strong>
                                <p class="text-muted small mb-0">أدخل المبالغ والتواريخ الصحيحة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-calendar-check text-info me-2 mt-1"></i>
                            <div>
                                <strong>حدد المواعيد</strong>
                                <p class="text-muted small mb-0">أضف يوم الاستحقاق للتذكير</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-sticky-note text-warning me-2 mt-1"></i>
                            <div>
                                <strong>استخدم الملاحظات</strong>
                                <p class="text-muted small mb-0">أضف تفاصيل مهمة للمراجعة</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // عناصر النموذج
        const amountInput = document.querySelector('input[name="amount"]');
        const dueDateInput = document.querySelector('input[name="due_date"]');
        
        // عناصر العرض
        const monthlyAmountDisplay = document.getElementById('monthlyAmountDisplay');
        const yearlyAmountDisplay = document.getElementById('yearlyAmountDisplay');
        const dueDateDisplay = document.getElementById('dueDateDisplay');
        
        // تحديث العرض
        function updateDisplay() {
            const monthlyAmount = parseFloat(amountInput.value) || 0;
            const yearlyAmount = monthlyAmount * 12;
            const dueDate = parseInt(dueDateInput.value) || 0;
            
            // عرض المبلغ الشهري
            monthlyAmountDisplay.textContent = monthlyAmount.toLocaleString('ar-IQ') + ' د.ع';
            
            // عرض المبلغ السنوي
            yearlyAmountDisplay.textContent = yearlyAmount.toLocaleString('ar-IQ') + ' د.ع';
            
            // عرض يوم الاستحقاق
            if (dueDate > 0 && dueDate <= 31) {
                dueDateDisplay.textContent = dueDate + ' من كل شهر';
            } else {
                dueDateDisplay.textContent = 'غير محدد';
            }
        }
        
        // ربط الأحداث
        amountInput.addEventListener('input', updateDisplay);
        dueDateInput.addEventListener('input', updateDisplay);
        
        // تحسين النموذج
        const form = document.getElementById('expenseForm');
        form.addEventListener('submit', function(e) {
            const name = document.querySelector('input[name="name"]').value.trim();
            const category = document.querySelector('select[name="category"]').value;
            const amount = parseFloat(amountInput.value) || 0;
            
            if (!name) {
                e.preventDefault();
                alert('يرجى إدخال اسم المستحق');
                document.querySelector('input[name="name"]').focus();
                return false;
            }
            
            if (!category) {
                e.preventDefault();
                alert('يرجى اختيار نوع المستحق');
                document.querySelector('select[name="category"]').focus();
                return false;
            }
            
            if (amount <= 0) {
                e.preventDefault();
                alert('يرجى إدخال مبلغ صحيح');
                amountInput.focus();
                return false;
            }
            
            // تأكيد الحفظ
            if (!confirm(`هل أنت متأكد من حفظ المستحق "${name}" بمبلغ ${amount.toLocaleString('ar-IQ')} دينار عراقي شهرياً؟`)) {
                e.preventDefault();
                return false;
            }
        });
        
        // تنسيق المدخلات الرقمية
        [amountInput, dueDateInput].forEach(input => {
            input.addEventListener('input', function() {
                // إزالة الأحرف غير الرقمية
                this.value = this.value.replace(/[^0-9]/g, '');
            });
        });
    });
</script>
{% endblock %}

{% extends "base.html" %}

{% block title %}إضافة دين جديد - نظام إدارة الرواتب{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="text-white text-center mb-4">
            <i class="fas fa-plus me-3"></i>
            إضافة دين جديد
        </h1>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-10">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-credit-card me-2"></i>
                    نموذج إضافة دين جديد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="debtForm">
                    <div class="row">
                        <!-- اسم الدائن -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-user text-primary me-1"></i>
                                اسم الدائن/الجهة *
                            </label>
                            <input type="text" name="creditor_name" class="form-control form-control-lg" 
                                   placeholder="اسم البنك أو الشخص أو الجهة" required>
                            <div class="form-text">أدخل اسم الجهة أو الشخص الذي تدين له</div>
                        </div>
                        
                        <!-- نوع الدين -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-tag text-info me-1"></i>
                                نوع الدين *
                            </label>
                            <select name="debt_type" class="form-select form-select-lg" required>
                                <option value="">اختر نوع الدين</option>
                                <option value="سيارة">سيارة</option>
                                <option value="منزل">منزل</option>
                                <option value="شخصي">شخصي</option>
                                <option value="تجاري">تجاري</option>
                                <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                                <option value="تعليم">تعليم</option>
                                <option value="طبي">طبي</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- المبلغ الإجمالي -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-money-bill-wave text-warning me-1"></i>
                                المبلغ الإجمالي (دينار عراقي) *
                            </label>
                            <input type="number" name="total_amount" class="form-control form-control-lg"
                                   placeholder="أدخل المبلغ الإجمالي للدين" required min="0" step="1">
                            <div class="form-text">المبلغ الكامل للدين</div>
                        </div>
                        
                        <!-- القسط الشهري -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-calendar-alt text-success me-1"></i>
                                القسط الشهري (دينار عراقي)
                            </label>
                            <input type="number" name="monthly_payment" class="form-control form-control-lg"
                                   placeholder="أدخل مبلغ القسط الشهري" min="0" step="1">
                            <div class="form-text">المبلغ المطلوب دفعه شهرياً (اختياري)</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- عدد الأقساط -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-list-ol text-info me-1"></i>
                                عدد الأقساط
                            </label>
                            <input type="number" name="installments_count" class="form-control form-control-lg" 
                                   placeholder="عدد الأقساط الإجمالي" min="1">
                            <div class="form-text">العدد الكلي للأقساط (اختياري)</div>
                        </div>
                        
                        <!-- يوم الاستحقاق -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-calendar-check text-warning me-1"></i>
                                يوم الاستحقاق الشهري
                            </label>
                            <input type="number" name="monthly_due_date" class="form-control form-control-lg" 
                                   placeholder="مثال: 15 (لليوم 15 من كل شهر)" min="1" max="31">
                            <div class="form-text">اليوم من الشهر المطلوب فيه السداد (اختياري)</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- تاريخ البداية -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-play text-success me-1"></i>
                                تاريخ بداية الدين
                            </label>
                            <input type="date" name="start_date" class="form-control form-control-lg">
                            <div class="form-text">تاريخ بداية الدين (اختياري)</div>
                        </div>
                        
                        <!-- تاريخ النهاية -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-stop text-danger me-1"></i>
                                تاريخ نهاية الدين المتوقع
                            </label>
                            <input type="date" name="end_date" class="form-control form-control-lg">
                            <div class="form-text">التاريخ المتوقع لانتهاء السداد (اختياري)</div>
                        </div>
                    </div>
                    
                    <!-- الوصف -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">
                            <i class="fas fa-sticky-note text-secondary me-1"></i>
                            وصف الدين
                        </label>
                        <textarea name="description" class="form-control" rows="4" 
                                  placeholder="أضف تفاصيل إضافية عن الدين مثل الغرض منه، شروط السداد، أو أي ملاحظات مهمة..."></textarea>
                        <div class="form-text">يمكنك إضافة تفاصيل مهمة عن الدين</div>
                    </div>
                    
                    <!-- معاينة المعلومات -->
                    <div class="mb-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="text-muted mb-3">معاينة المعلومات</h6>
                                <div class="row">
                                    <div class="col-md-4 text-center">
                                        <h6 class="text-muted">المبلغ الإجمالي</h6>
                                        <h4 class="text-warning mb-0" id="totalAmountDisplay">0 د.ع</h4>
                                    </div>
                                    <div class="col-md-4 text-center">
                                        <h6 class="text-muted">القسط الشهري</h6>
                                        <h4 class="text-success mb-0" id="monthlyPaymentDisplay">غير محدد</h4>
                                    </div>
                                    <div class="col-md-4 text-center">
                                        <h6 class="text-muted">عدد الأقساط</h6>
                                        <h4 class="text-info mb-0" id="installmentsDisplay">غير محدد</h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- أزرار العمليات -->
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <button type="submit" class="btn btn-success btn-lg w-100">
                                <i class="fas fa-save me-2"></i>
                                حفظ الدين
                            </button>
                        </div>
                        <div class="col-md-6 mb-2">
                            <a href="{{ url_for('debts') }}" class="btn btn-outline-secondary btn-lg w-100">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- نصائح مفيدة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-lightbulb me-2 text-warning"></i>
                    نصائح لإضافة الدين
                </h6>
                <div class="row">
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-check-circle text-success me-2 mt-1"></i>
                            <div>
                                <strong>كن دقيقاً</strong>
                                <p class="text-muted small mb-0">أدخل المبالغ والتواريخ الصحيحة كما هي في العقد</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-calendar-check text-info me-2 mt-1"></i>
                            <div>
                                <strong>حدد المواعيد</strong>
                                <p class="text-muted small mb-0">أضف تواريخ الاستحقاق لتتمكن من التذكير</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-sticky-note text-warning me-2 mt-1"></i>
                            <div>
                                <strong>استخدم الوصف</strong>
                                <p class="text-muted small mb-0">أضف تفاصيل مهمة مثل شروط السداد أو الفوائد</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // عناصر النموذج
        const totalAmountInput = document.querySelector('input[name="total_amount"]');
        const monthlyPaymentInput = document.querySelector('input[name="monthly_payment"]');
        const installmentsInput = document.querySelector('input[name="installments_count"]');
        
        // عناصر العرض
        const totalAmountDisplay = document.getElementById('totalAmountDisplay');
        const monthlyPaymentDisplay = document.getElementById('monthlyPaymentDisplay');
        const installmentsDisplay = document.getElementById('installmentsDisplay');
        
        // تحديث العرض
        function updateDisplay() {
            const totalAmount = parseFloat(totalAmountInput.value) || 0;
            const monthlyPayment = parseFloat(monthlyPaymentInput.value) || 0;
            const installments = parseInt(installmentsInput.value) || 0;
            
            // عرض المبلغ الإجمالي
            totalAmountDisplay.textContent = totalAmount.toLocaleString('ar-IQ') + ' د.ع';
            
            // عرض القسط الشهري
            if (monthlyPayment > 0) {
                monthlyPaymentDisplay.textContent = monthlyPayment.toLocaleString('ar-IQ') + ' د.ع';
            } else {
                monthlyPaymentDisplay.textContent = 'غير محدد';
            }
            
            // عرض عدد الأقساط
            if (installments > 0) {
                installmentsDisplay.textContent = installments + ' قسط';
            } else {
                installmentsDisplay.textContent = 'غير محدد';
            }
        }
        
        // ربط الأحداث
        totalAmountInput.addEventListener('input', updateDisplay);
        monthlyPaymentInput.addEventListener('input', updateDisplay);
        installmentsInput.addEventListener('input', updateDisplay);
        
        // تحسين النموذج
        const form = document.getElementById('debtForm');
        form.addEventListener('submit', function(e) {
            const creditorName = document.querySelector('input[name="creditor_name"]').value.trim();
            const debtType = document.querySelector('select[name="debt_type"]').value;
            const totalAmount = parseFloat(totalAmountInput.value) || 0;
            
            if (!creditorName) {
                e.preventDefault();
                alert('يرجى إدخال اسم الدائن/الجهة');
                document.querySelector('input[name="creditor_name"]').focus();
                return false;
            }
            
            if (!debtType) {
                e.preventDefault();
                alert('يرجى اختيار نوع الدين');
                document.querySelector('select[name="debt_type"]').focus();
                return false;
            }
            
            if (totalAmount <= 0) {
                e.preventDefault();
                alert('يرجى إدخال مبلغ إجمالي صحيح');
                totalAmountInput.focus();
                return false;
            }
            
            // تأكيد الحفظ
            if (!confirm(`هل أنت متأكد من حفظ دين "${creditorName}" بمبلغ ${totalAmount.toLocaleString('ar-IQ')} دينار عراقي؟`)) {
                e.preventDefault();
                return false;
            }
        });
        
        // تنسيق المدخلات الرقمية
        [totalAmountInput, monthlyPaymentInput].forEach(input => {
            input.addEventListener('input', function() {
                // إزالة الأحرف غير الرقمية
                this.value = this.value.replace(/[^0-9]/g, '');
            });
        });
        
        // تعيين التاريخ الحالي كقيمة افتراضية لتاريخ البداية
        const startDateInput = document.querySelector('input[name="start_date"]');
        if (startDateInput && !startDateInput.value) {
            const today = new Date().toISOString().split('T')[0];
            startDateInput.value = today;
        }
    });
</script>
{% endblock %}

{% extends "base.html" %}

{% block title %}تعديل الدين - نظام إدارة الرواتب{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="text-white text-center mb-4">
            <i class="fas fa-edit me-3"></i>
            تعديل دين "{{ debt.creditor_name }}"
        </h1>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-10">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>
                    نموذج تعديل الدين
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="editDebtForm">
                    <div class="row">
                        <!-- اسم الدائن -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-user text-primary me-1"></i>
                                اسم الدائن/الجهة *
                            </label>
                            <input type="text" name="creditor_name" class="form-control form-control-lg" 
                                   placeholder="اسم البنك أو الشخص أو الجهة" required
                                   value="{{ debt.creditor_name }}">
                            <div class="form-text">أدخل اسم الجهة أو الشخص الذي تدين له</div>
                        </div>
                        
                        <!-- نوع الدين -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-tag text-info me-1"></i>
                                نوع الدين *
                            </label>
                            <select name="debt_type" class="form-select form-select-lg" required>
                                <option value="">اختر نوع الدين</option>
                                <option value="سيارة" {% if debt.debt_type == 'سيارة' %}selected{% endif %}>سيارة</option>
                                <option value="منزل" {% if debt.debt_type == 'منزل' %}selected{% endif %}>منزل</option>
                                <option value="شخصي" {% if debt.debt_type == 'شخصي' %}selected{% endif %}>شخصي</option>
                                <option value="تجاري" {% if debt.debt_type == 'تجاري' %}selected{% endif %}>تجاري</option>
                                <option value="بطاقة ائتمان" {% if debt.debt_type == 'بطاقة ائتمان' %}selected{% endif %}>بطاقة ائتمان</option>
                                <option value="تعليم" {% if debt.debt_type == 'تعليم' %}selected{% endif %}>تعليم</option>
                                <option value="طبي" {% if debt.debt_type == 'طبي' %}selected{% endif %}>طبي</option>
                                <option value="أخرى" {% if debt.debt_type == 'أخرى' %}selected{% endif %}>أخرى</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- المبلغ الإجمالي -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-money-bill-wave text-warning me-1"></i>
                                المبلغ الإجمالي (دينار عراقي) *
                            </label>
                            <input type="number" name="total_amount" class="form-control form-control-lg"
                                   placeholder="أدخل المبلغ الإجمالي للدين" required min="0" step="1"
                                   value="{{ debt.total_amount|int }}">
                            <div class="form-text">المبلغ الكامل للدين</div>
                        </div>
                        
                        <!-- القسط الشهري -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-calendar-alt text-success me-1"></i>
                                القسط الشهري (دينار عراقي)
                            </label>
                            <input type="number" name="monthly_payment" class="form-control form-control-lg"
                                   placeholder="أدخل مبلغ القسط الشهري" min="0" step="1"
                                   value="{{ debt.monthly_payment|int if debt.monthly_payment else '' }}">
                            <div class="form-text">المبلغ المطلوب دفعه شهرياً (اختياري)</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- عدد الأقساط -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-list-ol text-info me-1"></i>
                                عدد الأقساط
                            </label>
                            <input type="number" name="installments_count" class="form-control form-control-lg" 
                                   placeholder="عدد الأقساط الإجمالي" min="1"
                                   value="{{ debt.installments_count if debt.installments_count else '' }}">
                            <div class="form-text">العدد الكلي للأقساط (اختياري)</div>
                        </div>
                        
                        <!-- يوم الاستحقاق -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-calendar-check text-warning me-1"></i>
                                يوم الاستحقاق الشهري
                            </label>
                            <input type="number" name="monthly_due_date" class="form-control form-control-lg" 
                                   placeholder="مثال: 15 (لليوم 15 من كل شهر)" min="1" max="31"
                                   value="{{ debt.monthly_due_date if debt.monthly_due_date else '' }}">
                            <div class="form-text">اليوم من الشهر المطلوب فيه السداد (اختياري)</div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- تاريخ البداية -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-play text-success me-1"></i>
                                تاريخ بداية الدين
                            </label>
                            <input type="date" name="start_date" class="form-control form-control-lg"
                                   value="{{ debt.start_date.strftime('%Y-%m-%d') if debt.start_date else '' }}">
                            <div class="form-text">تاريخ بداية الدين (اختياري)</div>
                        </div>
                        
                        <!-- تاريخ النهاية -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-stop text-danger me-1"></i>
                                تاريخ نهاية الدين المتوقع
                            </label>
                            <input type="date" name="end_date" class="form-control form-control-lg"
                                   value="{{ debt.end_date.strftime('%Y-%m-%d') if debt.end_date else '' }}">
                            <div class="form-text">التاريخ المتوقع لانتهاء السداد (اختياري)</div>
                        </div>
                    </div>
                    
                    <!-- الوصف -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">
                            <i class="fas fa-sticky-note text-secondary me-1"></i>
                            وصف الدين
                        </label>
                        <textarea name="description" class="form-control" rows="4" 
                                  placeholder="أضف تفاصيل إضافية عن الدين مثل الغرض منه، شروط السداد، أو أي ملاحظات مهمة...">{{ debt.description or '' }}</textarea>
                        <div class="form-text">يمكنك إضافة تفاصيل مهمة عن الدين</div>
                    </div>
                    
                    <!-- معلومات الدين الحالية -->
                    <div class="mb-4">
                        <div class="card border-info">
                            <div class="card-body">
                                <h6 class="card-title text-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    معلومات الدين الحالية
                                </h6>
                                <div class="row">
                                    <div class="col-md-3">
                                        <small class="text-muted">المبلغ الأصلي:</small>
                                        <p class="mb-1 text-warning fw-bold">{{ debt.total_amount|currency }}</p>
                                    </div>
                                    <div class="col-md-3">
                                        <small class="text-muted">المبلغ المتبقي:</small>
                                        <p class="mb-1 text-danger fw-bold">{{ debt.remaining_amount|currency }}</p>
                                    </div>
                                    <div class="col-md-3">
                                        <small class="text-muted">المبلغ المدفوع:</small>
                                        <p class="mb-1 text-success fw-bold">{{ (debt.total_amount - debt.remaining_amount)|currency }}</p>
                                    </div>
                                    <div class="col-md-3">
                                        <small class="text-muted">نسبة السداد:</small>
                                        <p class="mb-1 text-primary fw-bold">{{ "%.1f"|format(debt.progress_percentage) }}%</p>
                                    </div>
                                </div>
                                <div class="progress mt-2" style="height: 10px;">
                                    <div class="progress-bar 
                                        {% if debt.progress_percentage >= 75 %}bg-success
                                        {% elif debt.progress_percentage >= 50 %}bg-warning
                                        {% elif debt.progress_percentage >= 25 %}bg-info
                                        {% else %}bg-danger{% endif %}" 
                                        role="progressbar" 
                                        style="width: {{ debt.progress_percentage }}%">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- معاينة المعلومات الجديدة -->
                    <div class="mb-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="text-muted mb-3">معاينة المعلومات الجديدة</h6>
                                <div class="row">
                                    <div class="col-md-4 text-center">
                                        <h6 class="text-muted">المبلغ الإجمالي</h6>
                                        <h4 class="text-warning mb-0" id="totalAmountDisplay">{{ debt.total_amount|currency }}</h4>
                                    </div>
                                    <div class="col-md-4 text-center">
                                        <h6 class="text-muted">القسط الشهري</h6>
                                        <h4 class="text-success mb-0" id="monthlyPaymentDisplay">
                                            {% if debt.monthly_payment %}{{ debt.monthly_payment|currency }}{% else %}غير محدد{% endif %}
                                        </h4>
                                    </div>
                                    <div class="col-md-4 text-center">
                                        <h6 class="text-muted">عدد الأقساط</h6>
                                        <h4 class="text-info mb-0" id="installmentsDisplay">
                                            {% if debt.installments_count %}{{ debt.installments_count }} قسط{% else %}غير محدد{% endif %}
                                        </h4>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- أزرار العمليات -->
                    <div class="row">
                        <div class="col-md-4 mb-2">
                            <button type="submit" class="btn btn-warning btn-lg w-100">
                                <i class="fas fa-save me-2"></i>
                                حفظ التعديلات
                            </button>
                        </div>
                        <div class="col-md-4 mb-2">
                            <a href="{{ url_for('debts') }}" class="btn btn-outline-secondary btn-lg w-100">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                        <div class="col-md-4 mb-2">
                            <button type="button" class="btn btn-outline-danger btn-lg w-100" 
                                    onclick="confirmDelete({{ debt.id }}, '{{ debt.creditor_name }}')">
                                <i class="fas fa-trash me-2"></i>
                                حذف الدين
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- نصائح مفيدة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-lightbulb me-2 text-warning"></i>
                    نصائح للتعديل
                </h6>
                <div class="row">
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-exclamation-triangle text-warning me-2 mt-1"></i>
                            <div>
                                <strong>تحقق من البيانات</strong>
                                <p class="text-muted small mb-0">تأكد من صحة المبالغ والتواريخ قبل الحفظ</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-calculator text-info me-2 mt-1"></i>
                            <div>
                                <strong>المبلغ المتبقي</strong>
                                <p class="text-muted small mb-0">سيتم إعادة حساب المبلغ المتبقي تلقائياً</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-save text-success me-2 mt-1"></i>
                            <div>
                                <strong>احفظ التغييرات</strong>
                                <p class="text-muted small mb-0">لا تنس حفظ التعديلات بعد الانتهاء</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // عناصر النموذج
        const totalAmountInput = document.querySelector('input[name="total_amount"]');
        const monthlyPaymentInput = document.querySelector('input[name="monthly_payment"]');
        const installmentsInput = document.querySelector('input[name="installments_count"]');
        
        // عناصر العرض
        const totalAmountDisplay = document.getElementById('totalAmountDisplay');
        const monthlyPaymentDisplay = document.getElementById('monthlyPaymentDisplay');
        const installmentsDisplay = document.getElementById('installmentsDisplay');
        
        // تحديث العرض
        function updateDisplay() {
            const totalAmount = parseFloat(totalAmountInput.value) || 0;
            const monthlyPayment = parseFloat(monthlyPaymentInput.value) || 0;
            const installments = parseInt(installmentsInput.value) || 0;
            
            // عرض المبلغ الإجمالي
            totalAmountDisplay.textContent = totalAmount.toLocaleString('ar-IQ') + ' د.ع';
            
            // عرض القسط الشهري
            if (monthlyPayment > 0) {
                monthlyPaymentDisplay.textContent = monthlyPayment.toLocaleString('ar-IQ') + ' د.ع';
            } else {
                monthlyPaymentDisplay.textContent = 'غير محدد';
            }
            
            // عرض عدد الأقساط
            if (installments > 0) {
                installmentsDisplay.textContent = installments + ' قسط';
            } else {
                installmentsDisplay.textContent = 'غير محدد';
            }
        }
        
        // ربط الأحداث
        totalAmountInput.addEventListener('input', updateDisplay);
        monthlyPaymentInput.addEventListener('input', updateDisplay);
        installmentsInput.addEventListener('input', updateDisplay);
        
        // تحسين النموذج
        const form = document.getElementById('editDebtForm');
        form.addEventListener('submit', function(e) {
            const creditorName = document.querySelector('input[name="creditor_name"]').value.trim();
            const debtType = document.querySelector('select[name="debt_type"]').value;
            const totalAmount = parseFloat(totalAmountInput.value) || 0;
            
            if (!creditorName) {
                e.preventDefault();
                alert('يرجى إدخال اسم الدائن/الجهة');
                document.querySelector('input[name="creditor_name"]').focus();
                return false;
            }
            
            if (!debtType) {
                e.preventDefault();
                alert('يرجى اختيار نوع الدين');
                document.querySelector('select[name="debt_type"]').focus();
                return false;
            }
            
            if (totalAmount <= 0) {
                e.preventDefault();
                alert('يرجى إدخال مبلغ إجمالي صحيح');
                totalAmountInput.focus();
                return false;
            }
            
            // تأكيد الحفظ
            if (!confirm('هل أنت متأكد من حفظ التعديلات؟')) {
                e.preventDefault();
                return false;
            }
        });
        
        // تنسيق المدخلات الرقمية
        [totalAmountInput, monthlyPaymentInput].forEach(input => {
            input.addEventListener('input', function() {
                // إزالة الأحرف غير الرقمية
                this.value = this.value.replace(/[^0-9]/g, '');
            });
        });
    });
    
    function confirmDelete(debtId, creditorName) {
        if (confirm(`هل أنت متأكد من حذف دين "${creditorName}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
            // إنشاء نموذج مخفي لإرسال طلب الحذف
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/delete_debt/${debtId}`;
            document.body.appendChild(form);
            form.submit();
        }
    }
</script>
{% endblock %}

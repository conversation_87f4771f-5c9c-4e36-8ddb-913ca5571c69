{% extends "base.html" %}

{% block title %}إضافة دفعة جديدة - {{ debt.creditor_name }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="text-white text-center mb-4">
            <i class="fas fa-plus me-3"></i>
            إضافة دفعة جديدة لدين "{{ debt.creditor_name }}"
        </h1>
    </div>
</div>

<!-- معلومات الدين -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات الدين
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <h6 class="text-muted">الدائن/الجهة</h6>
                        <p class="mb-0 fw-bold">{{ debt.creditor_name }}</p>
                    </div>
                    <div class="col-md-3">
                        <h6 class="text-muted">نوع الدين</h6>
                        <p class="mb-0"><span class="badge bg-info">{{ debt.debt_type }}</span></p>
                    </div>
                    <div class="col-md-3">
                        <h6 class="text-muted">المبلغ المتبقي</h6>
                        <p class="mb-0 text-danger fw-bold">{{ debt.remaining_amount | currency }}</p>
                    </div>
                    <div class="col-md-3">
                        <h6 class="text-muted">نسبة السداد</h6>
                        <p class="mb-0 text-primary fw-bold">{{ "%.1f"|format(debt.progress_percentage) }}%</p>
                    </div>
                </div>
                
                <!-- شريط التقدم -->
                <div class="mt-3">
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar 
                            {% if debt.progress_percentage >= 75 %}bg-success
                            {% elif debt.progress_percentage >= 50 %}bg-warning
                            {% elif debt.progress_percentage >= 25 %}bg-info
                            {% else %}bg-danger{% endif %}" 
                            role="progressbar" 
                            style="width: {{ debt.progress_percentage }}%">
                            {{ "%.1f"|format(debt.progress_percentage) }}%
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نموذج إضافة الدفعة -->
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-receipt me-2"></i>
                    نموذج تسجيل دفعة جديدة
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="paymentForm">
                    <div class="row">
                        <!-- مبلغ الدفعة -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-money-bill text-success me-1"></i>
                                مبلغ الدفعة (دينار عراقي) *
                            </label>
                            <input type="number" name="amount" class="form-control form-control-lg"
                                   placeholder="أدخل مبلغ الدفعة" required min="1" step="1"
                                   max="{{ debt.remaining_amount|int }}">
                            <div class="form-text">
                                الحد الأقصى: {{ debt.remaining_amount | currency }}
                            </div>
                        </div>
                        
                        <!-- تاريخ الدفعة -->
                        <div class="col-md-6 mb-3">
                            <label class="form-label fw-bold">
                                <i class="fas fa-calendar text-primary me-1"></i>
                                تاريخ الدفعة *
                            </label>
                            <input type="date" name="payment_date" class="form-control form-control-lg" required>
                            <div class="form-text">تاريخ دفع المبلغ الفعلي</div>
                        </div>
                    </div>
                    
                    <!-- رقم الوصل -->
                    <div class="mb-3">
                        <label class="form-label fw-bold">
                            <i class="fas fa-receipt text-info me-1"></i>
                            رقم الوصل/الإيصال
                        </label>
                        <input type="text" name="receipt_number" class="form-control form-control-lg" 
                               placeholder="أدخل رقم الوصل أو الإيصال (اختياري)">
                        <div class="form-text">رقم الوصل المرجعي للدفعة (اختياري)</div>
                    </div>
                    
                    <!-- الملاحظات -->
                    <div class="mb-4">
                        <label class="form-label fw-bold">
                            <i class="fas fa-sticky-note text-secondary me-1"></i>
                            ملاحظات إضافية
                        </label>
                        <textarea name="notes" class="form-control" rows="3" 
                                  placeholder="أضف أي ملاحظات مهمة حول هذه الدفعة..."></textarea>
                        <div class="form-text">يمكنك إضافة تفاصيل إضافية عن الدفعة</div>
                    </div>
                    
                    <!-- معاينة الدفعة -->
                    <div class="mb-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="text-muted mb-3">معاينة الدفعة</h6>
                                <div class="row">
                                    <div class="col-md-4 text-center">
                                        <h6 class="text-muted">مبلغ الدفعة</h6>
                                        <h4 class="text-success mb-0" id="paymentAmountDisplay">0 د.ع</h4>
                                    </div>
                                    <div class="col-md-4 text-center">
                                        <h6 class="text-muted">المتبقي بعد الدفعة</h6>
                                        <h4 class="text-danger mb-0" id="remainingAfterDisplay">{{ debt.remaining_amount | currency }}</h4>
                                    </div>
                                    <div class="col-md-4 text-center">
                                        <h6 class="text-muted">نسبة السداد الجديدة</h6>
                                        <h4 class="text-primary mb-0" id="newProgressDisplay">{{ "%.1f"|format(debt.progress_percentage) }}%</h4>
                                    </div>
                                </div>
                                
                                <!-- شريط التقدم الجديد -->
                                <div class="mt-3">
                                    <div class="progress" style="height: 20px;">
                                        <div class="progress-bar bg-success" role="progressbar" 
                                             style="width: {{ debt.progress_percentage }}%" id="newProgressBar">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- أزرار العمليات -->
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <button type="submit" class="btn btn-success btn-lg w-100">
                                <i class="fas fa-save me-2"></i>
                                تسجيل الدفعة
                            </button>
                        </div>
                        <div class="col-md-6 mb-2">
                            <a href="{{ url_for('debt_details', debt_id=debt.id) }}" class="btn btn-outline-secondary btn-lg w-100">
                                <i class="fas fa-times me-2"></i>
                                إلغاء
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- نصائح مفيدة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-lightbulb me-2 text-warning"></i>
                    نصائح لتسجيل الدفعة
                </h6>
                <div class="row">
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-check-circle text-success me-2 mt-1"></i>
                            <div>
                                <strong>تأكد من المبلغ</strong>
                                <p class="text-muted small mb-0">تحقق من صحة المبلغ المدفوع قبل التسجيل</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-receipt text-info me-2 mt-1"></i>
                            <div>
                                <strong>احتفظ بالوصل</strong>
                                <p class="text-muted small mb-0">سجل رقم الوصل للمراجعة المستقبلية</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-calendar-check text-warning me-2 mt-1"></i>
                            <div>
                                <strong>التاريخ الصحيح</strong>
                                <p class="text-muted small mb-0">أدخل تاريخ الدفع الفعلي وليس تاريخ التسجيل</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // عناصر النموذج
        const amountInput = document.querySelector('input[name="amount"]');
        const paymentDateInput = document.querySelector('input[name="payment_date"]');
        
        // عناصر العرض
        const paymentAmountDisplay = document.getElementById('paymentAmountDisplay');
        const remainingAfterDisplay = document.getElementById('remainingAfterDisplay');
        const newProgressDisplay = document.getElementById('newProgressDisplay');
        const newProgressBar = document.getElementById('newProgressBar');
        
        // بيانات الدين
        const totalAmount = {{ debt.total_amount }};
        const currentRemaining = {{ debt.remaining_amount }};
        
        // تعيين التاريخ الحالي كقيمة افتراضية
        if (paymentDateInput && !paymentDateInput.value) {
            const today = new Date().toISOString().split('T')[0];
            paymentDateInput.value = today;
        }
        
        // تحديث العرض
        function updateDisplay() {
            const paymentAmount = parseFloat(amountInput.value) || 0;
            const remainingAfter = Math.max(0, currentRemaining - paymentAmount);
            const newProgress = ((totalAmount - remainingAfter) / totalAmount) * 100;
            
            // عرض مبلغ الدفعة
            paymentAmountDisplay.textContent = paymentAmount.toLocaleString('ar-IQ') + ' د.ع';
            
            // عرض المتبقي بعد الدفعة
            remainingAfterDisplay.textContent = remainingAfter.toLocaleString('ar-IQ') + ' د.ع';
            
            // عرض نسبة السداد الجديدة
            newProgressDisplay.textContent = newProgress.toFixed(1) + '%';
            
            // تحديث شريط التقدم
            newProgressBar.style.width = newProgress + '%';
            
            // تغيير لون شريط التقدم
            if (newProgress >= 100) {
                newProgressBar.className = 'progress-bar bg-success';
            } else if (newProgress >= 75) {
                newProgressBar.className = 'progress-bar bg-success';
            } else if (newProgress >= 50) {
                newProgressBar.className = 'progress-bar bg-warning';
            } else if (newProgress >= 25) {
                newProgressBar.className = 'progress-bar bg-info';
            } else {
                newProgressBar.className = 'progress-bar bg-danger';
            }
        }
        
        // ربط الأحداث
        amountInput.addEventListener('input', updateDisplay);
        
        // تحسين النموذج
        const form = document.getElementById('paymentForm');
        form.addEventListener('submit', function(e) {
            const paymentAmount = parseFloat(amountInput.value) || 0;
            const paymentDate = paymentDateInput.value;
            
            if (paymentAmount <= 0) {
                e.preventDefault();
                alert('يرجى إدخال مبلغ الدفعة');
                amountInput.focus();
                return false;
            }
            
            if (paymentAmount > currentRemaining) {
                e.preventDefault();
                alert(`مبلغ الدفعة (${paymentAmount.toLocaleString('ar-IQ')} د.ع) أكبر من المبلغ المتبقي (${currentRemaining.toLocaleString('ar-IQ')} د.ع)`);
                amountInput.focus();
                return false;
            }
            
            if (!paymentDate) {
                e.preventDefault();
                alert('يرجى اختيار تاريخ الدفعة');
                paymentDateInput.focus();
                return false;
            }
            
            // تأكيد التسجيل
            if (!confirm(`هل أنت متأكد من تسجيل دفعة بمبلغ ${paymentAmount.toLocaleString('ar-IQ')} دينار عراقي؟`)) {
                e.preventDefault();
                return false;
            }
        });
        
        // تنسيق المدخلات الرقمية
        amountInput.addEventListener('input', function() {
            // إزالة الأحرف غير الرقمية
            this.value = this.value.replace(/[^0-9]/g, '');
        });
    });
</script>
{% endblock %}

{% extends "base.html" %}

{% block title %}جميع الرواتب - نظام إدارة الرواتب{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-12">
        <h1 class="text-white text-center mb-4">
            <i class="fas fa-list me-3"></i>
            جميع الرواتب المسجلة
        </h1>
    </div>
</div>

<!-- أزرار العمليات -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-money-bill-wave me-2 text-primary"></i>
                        إدارة الرواتب
                    </h5>
                    <div>
                        <a href="{{ url_for('add_salary') }}" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>
                            إضافة راتب جديد
                        </a>
                        <a href="{{ url_for('index') }}" class="btn btn-outline-primary">
                            <i class="fas fa-home me-2"></i>
                            العودة للرئيسية
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول الرواتب -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-table me-2"></i>
                    قائمة الرواتب ({{ salaries|length }} راتب)
                </h5>
            </div>
            <div class="card-body">
                {% if salaries %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>#</th>
                                    <th>الشهر/السنة</th>
                                    <th>الراتب الأول</th>
                                    <th>الراتب الثاني</th>
                                    <th>المجموع</th>
                                    <th>الملاحظات</th>
                                    <th>تاريخ الإضافة</th>
                                    <th>العمليات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for salary in salaries %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>
                                        <span class="badge bg-primary fs-6">
                                            {{ salary.month }}/{{ salary.year }}
                                        </span>
                                    </td>
                                    <td class="text-success fw-bold">
                                        {{ salary.first_salary | currency }}
                                    </td>
                                    <td class="text-info fw-bold">
                                        {{ salary.second_salary | currency }}
                                    </td>
                                    <td class="text-primary fw-bold fs-5">
                                        {{ salary.total_salary | currency }}
                                    </td>
                                    <td>
                                        {% if salary.notes %}
                                            <span class="text-muted" title="{{ salary.notes }}">
                                                {{ salary.notes[:30] }}{% if salary.notes|length > 30 %}...{% endif %}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-muted">
                                        {{ salary.date_added.strftime('%Y-%m-%d %H:%M') }}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('edit_salary', salary_id=salary.id) }}" 
                                               class="btn btn-outline-warning" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" class="btn btn-outline-danger" 
                                                    title="حذف" onclick="confirmDelete({{ salary.id }}, '{{ salary.month }}/{{ salary.year }}')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- إحصائيات سريعة -->
                    <div class="row mt-4">
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <h6 class="text-muted mb-1">إجمالي الرواتب</h6>
                                <h4 class="text-primary mb-0">
                                    {{ salaries|sum(attribute='total_salary') | currency }}
                                </h4>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <h6 class="text-muted mb-1">متوسط الراتب</h6>
                                <h4 class="text-success mb-0">
                                    {{ (salaries|sum(attribute='total_salary') / salaries|length) | currency }}
                                </h4>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <h6 class="text-muted mb-1">أعلى راتب</h6>
                                <h4 class="text-warning mb-0">
                                    {{ salaries|max(attribute='total_salary')|attr('total_salary') | currency }}
                                </h4>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center p-3 bg-light rounded">
                                <h6 class="text-muted mb-1">أقل راتب</h6>
                                <h4 class="text-info mb-0">
                                    {{ salaries|min(attribute='total_salary')|attr('total_salary') | currency }}
                                </h4>
                            </div>
                        </div>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-4x text-muted mb-4"></i>
                        <h4 class="text-muted">لا توجد رواتب مسجلة</h4>
                        <p class="text-muted mb-4">ابدأ بإضافة راتبك الأول لتتمكن من تتبع دخلك الشهري</p>
                        <a href="{{ url_for('add_salary') }}" class="btn btn-primary btn-lg">
                            <i class="fas fa-plus me-2"></i>
                            إضافة راتب جديد
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- نصائح مفيدة -->
{% if salaries %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">
                    <i class="fas fa-lightbulb me-2 text-warning"></i>
                    نصائح مفيدة
                </h6>
                <div class="row">
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-chart-line text-success me-2 mt-1"></i>
                            <div>
                                <strong>تتبع النمو</strong>
                                <p class="text-muted small mb-0">راقب نمو راتبك عبر الأشهر لتقييم تطورك المهني</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-calendar-check text-info me-2 mt-1"></i>
                            <div>
                                <strong>انتظام التسجيل</strong>
                                <p class="text-muted small mb-0">سجل راتبك فور استلامه لضمان دقة السجلات</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-start">
                            <i class="fas fa-sticky-note text-warning me-2 mt-1"></i>
                            <div>
                                <strong>استخدم الملاحظات</strong>
                                <p class="text-muted small mb-0">أضف ملاحظات مهمة مثل المكافآت أو الخصومات</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    function confirmDelete(salaryId, monthYear) {
        if (confirm(`هل أنت متأكد من حذف راتب ${monthYear}؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
            // إنشاء نموذج مخفي لإرسال طلب الحذف
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/delete_salary/${salaryId}`;
            document.body.appendChild(form);
            form.submit();
        }
    }
    
    // تحسين عرض الجدول على الأجهزة الصغيرة
    document.addEventListener('DOMContentLoaded', function() {
        if (window.innerWidth < 768) {
            const table = document.querySelector('.table');
            if (table) {
                table.style.fontSize = '0.85rem';
            }
        }
    });
</script>
{% endblock %}
